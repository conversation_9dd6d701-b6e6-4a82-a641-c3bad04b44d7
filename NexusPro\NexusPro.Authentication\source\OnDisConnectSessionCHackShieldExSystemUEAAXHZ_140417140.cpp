/*
 * OnDisconnectSession.cpp
 * Original Function: ?OnDisConnectSession@CHackShieldExSystem@@XH@Z
 * Original Address: 0x140417140
 *
 * Description: Handles session disconnection events for the HackShield Ex System.
 * This function is called when a session disconnects and performs cleanup
 * operations for the HackShield parameters associated with that session.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * Handles session disconnection for HackShield Ex System
 * @param this Pointer to the CHackShieldExSystem instance
 * @param n Session identifier/number to disconnect
 */
void CHackShieldExSystem::OnDisConnectSession(CHackShieldExSystem *this, int n) {
    __int64 *v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-38h]@1
    BASE_HACKSHEILD_PARAM *v5; // [sp+20h] [bp-18h]@4
    CHackShieldExSystem *v6; // [sp+40h] [bp+8h]@1

    v6 = this;
    v2 = &v4;

    // Initialize stack buffer with debug pattern
    for (i = 12LL; i; --i) {
        *(DWORD *)v2 = 0xCCCCCCCC;
        v2 = (__int64 *)((char *)v2 + 4);
    }

    // Get HackShield parameters for this session
    v5 = CHackShieldExSystem::GetParam(v6, n);

    // If parameters exist, call the OnDisConnect callback for cleanup
    if (v5) {
        ((void (*)(BASE_HACKSHEILD_PARAM *))v5->vfptr->OnDisConnect)(v5);
    }
}



